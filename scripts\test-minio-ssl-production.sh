#!/bin/bash

# MinIO SSL Test Script for Production/Staging
# This script tests the SSL configuration of MinIO on production IP

set -e

PRODUCTION_IP="*************"
ENVIRONMENT=${1:-production}

echo "🧪 Testing MinIO SSL Configuration for $ENVIRONMENT environment..."
echo "Production IP: $PRODUCTION_IP"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Set ports based on environment
case $ENVIRONMENT in
    "production"|"prod")
        API_PORT="9000"
        CONSOLE_PORT="9001"
        ;;
    "staging"|"stage")
        API_PORT="9003"
        CONSOLE_PORT="9004"
        ;;
    *)
        echo -e "${RED}❌ Unknown environment: $ENVIRONMENT${NC}"
        echo "Usage: $0 [production|staging]"
        exit 1
        ;;
esac

echo "Testing ports: API=$API_PORT, Console=$CONSOLE_PORT"

# Test functions
test_certificate_files() {
    echo -e "${BLUE}📁 Checking certificate files...${NC}"
    
    if [ -f "./certs/minio/private.key" ]; then
        echo -e "${GREEN}✅ Private key exists${NC}"
    else
        echo -e "${RED}❌ Private key missing${NC}"
        return 1
    fi
    
    if [ -f "./certs/minio/public.crt" ]; then
        echo -e "${GREEN}✅ Public certificate exists${NC}"
    else
        echo -e "${RED}❌ Public certificate missing${NC}"
        return 1
    fi
    
    # Check if certificate includes production IP
    if command -v openssl >/dev/null 2>&1; then
        echo -e "${BLUE}🔍 Checking certificate for production IP...${NC}"
        if openssl x509 -in "./certs/minio/public.crt" -text -noout | grep -q "$PRODUCTION_IP"; then
            echo -e "${GREEN}✅ Certificate includes production IP ($PRODUCTION_IP)${NC}"
        else
            echo -e "${RED}❌ Certificate does not include production IP ($PRODUCTION_IP)${NC}"
            echo -e "${YELLOW}   Run: ./scripts/generate-minio-certs-production.sh${NC}"
            return 1
        fi
        
        echo -e "${BLUE}📋 Certificate SANs:${NC}"
        openssl x509 -in "./certs/minio/public.crt" -text -noout | grep -A 10 "Subject Alternative Name:" || echo "No SANs found"
    fi
}

test_container_status() {
    echo -e "${BLUE}🐳 Checking container status...${NC}"
    
    if docker ps | grep -q "jadwa-minio"; then
        echo -e "${GREEN}✅ MinIO container is running${NC}"
        
        # Show container details
        docker ps --filter "name=jadwa-minio" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    else
        echo -e "${RED}❌ MinIO container is not running${NC}"
        return 1
    fi
}

test_local_ssl_connectivity() {
    echo -e "${BLUE}🔗 Testing local SSL connectivity...${NC}"
    
    # Test API endpoint locally (container internal)
    if curl -k -s -f "https://localhost:9000/minio/health/live" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ MinIO API (localhost:9000) is accessible via HTTPS${NC}"
    else
        echo -e "${RED}❌ MinIO API (localhost:9000) is not accessible via HTTPS${NC}"
        return 1
    fi
}

test_production_ssl_connectivity() {
    echo -e "${BLUE}🌐 Testing production SSL connectivity...${NC}"
    
    # Test API endpoint on production IP
    echo -e "${BLUE}Testing API endpoint: https://$PRODUCTION_IP:$API_PORT${NC}"
    if timeout 10 curl -k -s -f "https://$PRODUCTION_IP:$API_PORT/minio/health/live" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ MinIO API ($PRODUCTION_IP:$API_PORT) is accessible via HTTPS${NC}"
    else
        echo -e "${YELLOW}⚠️  MinIO API ($PRODUCTION_IP:$API_PORT) is not accessible${NC}"
        echo -e "${YELLOW}   This may be expected if testing locally or if firewall blocks access${NC}"
    fi
    
    # Test Console endpoint on production IP
    echo -e "${BLUE}Testing Console endpoint: https://$PRODUCTION_IP:$CONSOLE_PORT${NC}"
    if timeout 10 curl -k -s -f "https://$PRODUCTION_IP:$CONSOLE_PORT" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ MinIO Console ($PRODUCTION_IP:$CONSOLE_PORT) is accessible via HTTPS${NC}"
    else
        echo -e "${YELLOW}⚠️  MinIO Console ($PRODUCTION_IP:$CONSOLE_PORT) test inconclusive${NC}"
        echo -e "${YELLOW}   This may be expected if testing locally or if firewall blocks access${NC}"
    fi
}

test_certificate_details() {
    echo -e "${BLUE}📋 Certificate validation details...${NC}"
    
    if command -v openssl >/dev/null 2>&1; then
        # Show certificate expiry
        EXPIRY=$(openssl x509 -in "./certs/minio/public.crt" -noout -enddate | cut -d= -f2)
        echo -e "${BLUE}Certificate expires: ${EXPIRY}${NC}"
        
        # Calculate days until expiry
        if command -v date >/dev/null 2>&1; then
            EXPIRY_TIMESTAMP=$(date -d "$EXPIRY" +%s 2>/dev/null || date -j -f "%b %d %H:%M:%S %Y %Z" "$EXPIRY" +%s 2>/dev/null || echo "0")
            CURRENT_TIMESTAMP=$(date +%s)
            DAYS_UNTIL_EXPIRY=$(( (EXPIRY_TIMESTAMP - CURRENT_TIMESTAMP) / 86400 ))
            
            if [ "$DAYS_UNTIL_EXPIRY" -gt 30 ]; then
                echo -e "${GREEN}✅ Certificate valid for $DAYS_UNTIL_EXPIRY more days${NC}"
            elif [ "$DAYS_UNTIL_EXPIRY" -gt 0 ]; then
                echo -e "${YELLOW}⚠️  Certificate expires in $DAYS_UNTIL_EXPIRY days${NC}"
            else
                echo -e "${RED}❌ Certificate has expired!${NC}"
            fi
        fi
        
        # Show all certificate details
        echo -e "${BLUE}📜 Full certificate details:${NC}"
        openssl x509 -in "./certs/minio/public.crt" -text -noout | grep -E "(Subject:|DNS:|IP Address:)"
    else
        echo -e "${YELLOW}⚠️  OpenSSL not available for detailed certificate testing${NC}"
    fi
}

test_docker_logs() {
    echo -e "${BLUE}📝 Checking MinIO logs for SSL-related messages...${NC}"
    
    if docker ps | grep -q "jadwa-minio"; then
        echo -e "${BLUE}Recent MinIO logs:${NC}"
        docker logs --tail 20 jadwa-minio 2>&1 | grep -E "(SSL|TLS|certificate|https|error|Error)" || echo "No SSL-related log messages found"
    else
        echo -e "${RED}❌ MinIO container not running${NC}"
    fi
}

# Main test execution
main() {
    echo -e "${BLUE}🚀 Starting MinIO SSL tests for $ENVIRONMENT environment...${NC}"
    echo ""
    
    local failed_tests=0
    
    # Run tests
    test_certificate_files || ((failed_tests++))
    echo ""
    
    test_container_status || ((failed_tests++))
    echo ""
    
    test_local_ssl_connectivity || ((failed_tests++))
    echo ""
    
    test_production_ssl_connectivity
    echo ""
    
    test_certificate_details
    echo ""
    
    test_docker_logs
    echo ""
    
    # Summary
    if [ $failed_tests -eq 0 ]; then
        echo -e "${GREEN}🎉 Core SSL tests passed!${NC}"
        echo -e "${GREEN}MinIO is properly configured with SSL for $ENVIRONMENT${NC}"
        echo ""
        echo -e "${BLUE}Access URLs for $ENVIRONMENT:${NC}"
        echo "  MinIO API: https://$PRODUCTION_IP:$API_PORT"
        echo "  MinIO Console: https://$PRODUCTION_IP:$CONSOLE_PORT"
        echo "  Username: minioadmin"
        echo "  Password: minioadmin"
        echo ""
        echo -e "${YELLOW}Note: External connectivity depends on firewall and network configuration${NC}"
    else
        echo -e "${RED}❌ $failed_tests critical test(s) failed${NC}"
        echo -e "${YELLOW}Please check the configuration and try again${NC}"
        return 1
    fi
}

# Check if we're in the right directory
if [ ! -f "docker-compose.yaml" ]; then
    echo -e "${RED}❌ Please run this script from the project root directory${NC}"
    exit 1
fi

# Run main function
main
