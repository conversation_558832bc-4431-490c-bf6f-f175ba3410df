# MinIO SSL Configuration Guide

This guide explains how to set up SSL/TLS encryption for MinIO in the Jadwa Fund Management System using self-signed certificates.

## Overview

The MinIO SSL setup includes:
- Self-signed SSL certificate generation
- Docker Compose configuration updates
- Application configuration updates
- Certificate management and renewal

## Prerequisites

- Docker and Docker Compose installed
- OpenSSL installed (for certificate generation)
- Access to the project directory

## Quick Start

### 1. Generate SSL Certificates

#### On Linux/macOS:
```bash
chmod +x scripts/generate-minio-certs.sh
./scripts/generate-minio-certs.sh
```

#### On Windows (PowerShell):
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\scripts\generate-minio-certs.ps1
```

### 2. Start MinIO with SSL

```bash
# Development environment
docker-compose -f docker-compose.yaml -f docker-compose.development.yaml up -d

# Production environment
docker-compose -f docker-compose.yaml -f docker-compose.production.yaml up -d

# Staging environment
docker-compose -f docker-compose.yaml -f docker-compose.staging.yaml up -d
```

### 3. Access MinIO

- **API Endpoint**: https://localhost:9000
- **Web Console**: https://localhost:9001
- **Username**: minioadmin
- **Password**: minioadmin

⚠️ **Note**: Browsers will show security warnings for self-signed certificates. Click "Advanced" and "Proceed to localhost" to continue.

## Certificate Details

### Generated Files

The certificate generation creates the following files in `./certs/minio/`:

- `private.key` - Private key (2048-bit RSA)
- `public.crt` - Public certificate (365 days validity)
- `combined.pem` - Combined certificate and key
- `cert.csr` - Certificate signing request
- `cert.conf` - OpenSSL configuration

### Subject Alternative Names (SANs)

The certificate includes the following SANs for compatibility:
- `DNS:minio` (container name)
- `DNS:localhost` (local access)
- `DNS:jadwa-minio` (container name)
- `IP:127.0.0.1` (localhost IP)
- `IP:0.0.0.0` (all interfaces)

## Docker Configuration Changes

### Main Changes in docker-compose.yaml

1. **SSL Certificate Volume Mount**:
   ```yaml
   volumes:
     - ./certs/minio:/root/.minio/certs:ro
   ```

2. **Environment Variables**:
   ```yaml
   environment:
     MINIO_SERVER_URL: https://localhost:9000
     MINIO_BROWSER_REDIRECT_URL: https://localhost:9001
   ```

3. **Updated Health Check**:
   ```yaml
   healthcheck:
     test: ["CMD", "curl", "-k", "-f", "https://localhost:9000/minio/health/live"]
   ```

4. **SSL Command Flag**:
   ```yaml
   command: server /data --console-address ":9001" --certs-dir /root/.minio/certs
   ```

### Application Configuration Updates

All environment-specific Docker Compose files now have:
```yaml
environment:
  - MinIOConfiguration__UseSSL=true
```

## Application Settings

Update your `appsettings.json` files to use SSL:

```json
{
  "MinIOConfiguration": {
    "Endpoint": "minio:9000",
    "UseSSL": true,
    "AccessKey": "minioadmin",
    "SecretKey": "minioadmin"
  }
}
```

## Troubleshooting

### Common Issues

1. **Certificate Not Found Error**:
   - Ensure certificates are generated in `./certs/minio/`
   - Check file permissions (private.key should be 600)
   - Verify volume mount in docker-compose.yaml

2. **SSL Handshake Failures**:
   - Verify certificate SANs include the hostname you're using
   - Check that the certificate hasn't expired
   - Ensure the MinIO client is configured to use SSL

3. **Browser Security Warnings**:
   - This is expected with self-signed certificates
   - Click "Advanced" → "Proceed to localhost"
   - For production, use certificates from a trusted CA

4. **MinIO Setup Container Fails**:
   - The setup container uses `--insecure` flag for self-signed certificates
   - Increase sleep time if MinIO takes longer to start with SSL

### Verification Commands

Check certificate validity:
```bash
openssl x509 -in ./certs/minio/public.crt -text -noout
```

Test SSL connection:
```bash
curl -k https://localhost:9000/minio/health/live
```

Check MinIO logs:
```bash
docker logs jadwa-minio
```

## Security Considerations

### Self-Signed Certificates

- ✅ Encrypts data in transit
- ✅ Suitable for development and internal use
- ❌ Browsers show security warnings
- ❌ Not suitable for public-facing production

### Production Recommendations

For production environments, consider:

1. **Use CA-signed certificates** from Let's Encrypt, DigiCert, etc.
2. **Implement certificate rotation** (current certificates expire in 365 days)
3. **Use strong passwords** (change default minioadmin credentials)
4. **Enable access logging** and monitoring
5. **Implement proper firewall rules**

## Certificate Renewal

Certificates expire after 365 days. To renew:

1. Run the certificate generation script again
2. Restart the MinIO container:
   ```bash
   docker-compose restart minio
   ```

## Integration with .NET Application

The .NET application automatically handles SSL when `UseSSL=true` is set in configuration. The MinIO client library will:

- Use HTTPS for all API calls
- Validate SSL certificates (may need custom validation for self-signed)
- Handle SSL handshake automatically

### Custom Certificate Validation (if needed)

If you encounter SSL validation issues, you may need to add custom certificate validation in your application:

```csharp
// In your MinIO service configuration
ServicePointManager.ServerCertificateValidationCallback = 
    (sender, certificate, chain, sslPolicyErrors) => true; // Only for development!
```

⚠️ **Warning**: Only use custom certificate validation in development environments.

## Monitoring and Logs

Monitor MinIO SSL status:
- Check Docker container logs: `docker logs jadwa-minio`
- Monitor SSL certificate expiration
- Set up alerts for certificate renewal

## Support

For issues related to:
- Certificate generation: Check OpenSSL installation and permissions
- Docker configuration: Verify volume mounts and environment variables
- Application integration: Check MinIO client configuration and SSL settings
