services:
  redis:
    image: redis:latest
    container_name: redis
    restart: always
    ports:
      - "6379:6379"
    networks:
      - jadwa-network

  minio:
    image: quay.io/minio/minio:latest
    container_name: jadwa-minio
    restart: always
    ports:
      - "9000:9000"   # MinIO API port (HTTPS)
      - "9001:9001"   # MinIO Console (Web UI) port (HTTPS)
    environment:
      # Default credentials (change these for production)
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
      # Set default region
      MINIO_REGION: us-east-1
      # SSL Configuration
      MINIO_SERVER_URL: https://localhost:9000
      MINIO_BROWSER_REDIRECT_URL: https://localhost:9001
    volumes:
      # Persist MinIO data
      - minio_data:/data
      # SSL certificates
      - ./certs/minio:/root/.minio/certs:ro
    command: server /data --console-address ":9001" --certs-dir /root/.minio/certs
    networks:
      - jadwa-network
    healthcheck:
      test: ["CMD", "curl", "-k", "-f", "https://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
      start_period: 30s

  minio-setup:
    image: quay.io/minio/mc:latest
    container_name: jadwa-minio-setup
    depends_on:
      minio:
        condition: service_healthy
    volumes:
      # Mount certificates for SSL verification
      - ./certs/minio:/certs:ro
    entrypoint: >
      /bin/sh -c "
      echo 'Waiting for MinIO to be ready...';
      sleep 15;
      echo 'Setting up MinIO alias with SSL...';
      /usr/bin/mc alias set jadwa-minio https://minio:9000 minioadmin minioadmin --insecure;
      echo 'Creating default buckets...';
      /usr/bin/mc mb jadwa-minio/fund --ignore-existing;
      /usr/bin/mc mb jadwa-minio/user --ignore-existing;
      /usr/bin/mc mb jadwa-minio/other --ignore-existing;
      /usr/bin/mc mb jadwa-minio/resolution --ignore-existing;
      /usr/bin/mc mb jadwa-minio/documents --ignore-existing;
      echo 'Setting bucket policies for no expiry...';
      /usr/bin/mc anonymous set download jadwa-minio/fund;
      /usr/bin/mc anonymous set download jadwa-minio/user;
      /usr/bin/mc anonymous set download jadwa-minio/other;
      /usr/bin/mc anonymous set download jadwa-minio/resolution;
      /usr/bin/mc anonymous set download jadwa-minio/documents;
      echo 'Removing any existing lifecycle policies...';
      /usr/bin/mc ilm remove jadwa-minio/fund --force || true;
      /usr/bin/mc ilm remove jadwa-minio/user --force || true;
      /usr/bin/mc ilm remove jadwa-minio/other --force || true;
      /usr/bin/mc ilm remove jadwa-minio/resolution --force || true;
      /usr/bin/mc ilm remove jadwa-minio/documents --force || true;
      echo 'MinIO setup completed successfully with SSL and no expiry policies!';
      echo 'MinIO Console available at: https://localhost:9001';
      echo 'MinIO API available at: https://localhost:9000';
      echo 'Username: minioadmin';
      echo 'Password: minioadmin';
      echo 'Note: Self-signed certificate - browsers will show security warnings';"
    networks:
      - jadwa-network

  jadwa-api:
    build: ./
    container_name: jadwa-api
    restart: always
    depends_on:
      - redis
      - minio
    networks:
      - jadwa-network
    environment:
      - ConnectionStrings__Redis=redis:6379
      - ASPNETCORE_Kestrel__Endpoints__Https__Url=https://+:8081
      - ASPNETCORE_Kestrel__Endpoints__Http__Url=http://+:8080
      - ASPNETCORE_Kestrel__Endpoints__Https__Certificate__Path=/https/aspnetapp.pfx
      - ASPNETCORE_Kestrel__Endpoints__Https__Certificate__Password=123456
      # MinIO configuration for containerized environment

    volumes:
      - ./aspnetapp.pfx:/https/aspnetapp.pfx:ro
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro

volumes:
  minio_data:
    driver: local

networks:
  jadwa-network:
    driver: bridge