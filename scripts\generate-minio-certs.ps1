# MinIO SSL Certificate Generation Script for Windows PowerShell
# This script generates self-signed SSL certificates for MinIO using OpenSSL

param(
    [string]$CertDir = "./certs/minio",
    [int]$ValidityDays = 365,
    [string]$CommonName = "minio"
)

# Configuration
$Country = "SA"
$State = "Riyadh"
$City = "Riyadh"
$Organization = "Jadwa Fund Management"
$OrganizationalUnit = "IT Department"
$Email = "<EMAIL>"

Write-Host "🔐 Generating MinIO SSL Certificates..." -ForegroundColor Green
Write-Host "Certificate Directory: $CertDir" -ForegroundColor Yellow
Write-Host "Validity: $ValidityDays days" -ForegroundColor Yellow
Write-Host "Common Name: $CommonName" -ForegroundColor Yellow

# Check if OpenSSL is available
try {
    $null = Get-Command openssl -ErrorAction Stop
    Write-Host "✅ OpenSSL found" -ForegroundColor Green
} catch {
    Write-Host "❌ OpenSSL not found. Please install OpenSSL first." -ForegroundColor Red
    Write-Host "You can download it from: https://slproweb.com/products/Win32OpenSSL.html" -ForegroundColor Yellow
    exit 1
}

# Create certificate directory
if (!(Test-Path $CertDir)) {
    New-Item -ItemType Directory -Path $CertDir -Force | Out-Null
    Write-Host "📁 Created certificate directory: $CertDir" -ForegroundColor Green
}

# Generate private key
Write-Host "📝 Generating private key..." -ForegroundColor Cyan
& openssl genrsa -out "$CertDir/private.key" 2048

# Create certificate configuration file
Write-Host "📝 Creating certificate configuration..." -ForegroundColor Cyan
$certConfig = @"
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C=$Country
ST=$State
L=$City
O=$Organization
OU=$OrganizationalUnit
CN=$CommonName
emailAddress=$Email

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = minio
DNS.2 = localhost
DNS.3 = jadwa-minio
DNS.4 = 127.0.0.1
IP.1 = 127.0.0.1
IP.2 = 0.0.0.0
"@

$certConfig | Out-File -FilePath "$CertDir/cert.conf" -Encoding ASCII

# Generate certificate signing request
Write-Host "📝 Generating certificate signing request..." -ForegroundColor Cyan
& openssl req -new -key "$CertDir/private.key" -out "$CertDir/cert.csr" -config "$CertDir/cert.conf"

# Generate self-signed certificate
Write-Host "📝 Generating self-signed certificate..." -ForegroundColor Cyan
& openssl x509 -req -in "$CertDir/cert.csr" -signkey "$CertDir/private.key" -out "$CertDir/public.crt" -days $ValidityDays -extensions v3_req -extfile "$CertDir/cert.conf"

# Create combined certificate file
Write-Host "📝 Creating combined certificate file..." -ForegroundColor Cyan
Get-Content "$CertDir/public.crt", "$CertDir/private.key" | Set-Content "$CertDir/combined.pem"

Write-Host "✅ Certificate generation completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Certificate Details:" -ForegroundColor Yellow
& openssl x509 -in "$CertDir/public.crt" -text -noout | Select-String -Pattern "Subject:"
& openssl x509 -in "$CertDir/public.crt" -text -noout | Select-String -Pattern "DNS:" -Context 0,5

Write-Host ""
Write-Host "📁 Generated Files:" -ForegroundColor Yellow
Write-Host "  - Private Key: $CertDir/private.key"
Write-Host "  - Public Certificate: $CertDir/public.crt"
Write-Host "  - Combined PEM: $CertDir/combined.pem"
Write-Host "  - CSR: $CertDir/cert.csr"
Write-Host "  - Config: $CertDir/cert.conf"

Write-Host ""
Write-Host "🔍 Certificate Validity:" -ForegroundColor Yellow
& openssl x509 -in "$CertDir/public.crt" -noout -dates

Write-Host ""
Write-Host "⚠️  Note: This is a self-signed certificate. Browsers will show security warnings." -ForegroundColor Red
Write-Host "   For production use, consider using certificates from a trusted CA." -ForegroundColor Yellow
Write-Host ""
Write-Host "🐳 Next steps:" -ForegroundColor Green
Write-Host "   1. Update docker-compose.yaml to use SSL configuration"
Write-Host "   2. Update application configuration to use UseSSL=true"
Write-Host "   3. Restart MinIO container"
