# MinIO SSL Production Quick Reference

## 🌐 Environment URLs

### Development (localhost)
- **API**: https://localhost:9000
- **Console**: https://localhost:9001

### Production (*************)
- **API**: https://*************:9000
- **Console**: https://*************:9001

### Staging (*************)
- **API**: https://*************:9003
- **Console**: https://*************:9004

**Credentials**: minioadmin / minioadmin

## 🚀 Quick Setup Commands

### Generate Certificates (with Production IP)
```bash
# Linux/macOS
./scripts/generate-minio-certs-production.sh

# Windows
.\scripts\generate-minio-certs.ps1
```

### Start Services
```bash
# Development
./scripts/setup-minio-ssl.sh development

# Production
./scripts/setup-minio-ssl.sh production

# Staging
./scripts/setup-minio-ssl.sh staging
```

### Test SSL Configuration
```bash
# Development
./scripts/test-minio-ssl.sh

# Production/Staging
./scripts/test-minio-ssl-production.sh production
./scripts/test-minio-ssl-production.sh staging
```

## 🔧 Manual Docker Commands

### Development
```bash
docker-compose -f docker-compose.yaml -f docker-compose.development.yaml up -d
```

### Production
```bash
docker-compose -f docker-compose.yaml -f docker-compose.production.yaml up -d
```

### Staging
```bash
docker-compose -f docker-compose.yaml -f docker-compose.staging.yaml up -d
```

## 🔍 Verification Commands

### Check Containers
```bash
docker ps | grep jadwa-minio
```

### Test SSL Endpoints
```bash
# Local
curl -k https://localhost:9000/minio/health/live

# Production
curl -k https://*************:9000/minio/health/live

# Staging
curl -k https://*************:9003/minio/health/live
```

### View Logs
```bash
docker logs jadwa-minio
docker logs jadwa-minio-setup
```

### Check Certificate
```bash
openssl x509 -in ./certs/minio/public.crt -text -noout | grep -A 10 "Subject Alternative Name"
```

## 🛠️ Troubleshooting

### Restart MinIO
```bash
docker-compose restart minio
```

### Regenerate Certificates
```bash
rm -rf ./certs/minio/*
./scripts/generate-minio-certs-production.sh
docker-compose restart minio
```

### Check Certificate Expiry
```bash
openssl x509 -in ./certs/minio/public.crt -noout -dates
```

### View SSL Connection Details
```bash
openssl s_client -connect *************:9000 -servername *************
```

## 📁 Important Files

### Certificates
- `./certs/minio/private.key` - Private key
- `./certs/minio/public.crt` - Public certificate
- `./certs/minio/combined.pem` - Combined certificate

### Configuration
- `docker-compose.yaml` - Base configuration
- `docker-compose.production.yaml` - Production overrides
- `docker-compose.staging.yaml` - Staging overrides
- `docker-compose.development.yaml` - Development overrides

### Scripts
- `scripts/generate-minio-certs-production.sh` - Certificate generation
- `scripts/setup-minio-ssl.sh` - Complete setup
- `scripts/test-minio-ssl-production.sh` - Production testing

## 🔐 Security Notes

- ⚠️ Self-signed certificates will show browser warnings
- 🔄 Certificates expire after 365 days
- 🔑 Change default credentials for production
- 🛡️ Configure firewall rules for ports 9000, 9001, 9003, 9004

## 📞 Emergency Commands

### Stop All Services
```bash
docker-compose down
```

### Remove All Containers and Volumes
```bash
docker-compose down -v
```

### View All MinIO-related Containers
```bash
docker ps -a | grep minio
```

### Clean Up Everything
```bash
docker-compose down -v
docker system prune -f
```
