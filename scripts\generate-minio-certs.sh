#!/bin/bash

# MinIO SSL Certificate Generation Script
# This script generates self-signed SSL certificates for MinIO

set -e

# Configuration
CERT_DIR="./certs/minio"
CERT_VALIDITY_DAYS=365
COUNTRY="SA"
STATE="Riyadh"
CITY="Riyadh"
ORGANIZATION="Jadwa Fund Management"
ORGANIZATIONAL_UNIT="IT Department"
COMMON_NAME="minio"
EMAIL="<EMAIL>"

# Subject Alternative Names (SANs) for MinIO
# Include localhost, container name, and any other hostnames you might use
SANS="DNS:minio,DNS:localhost,DNS:jadwa-minio,IP:127.0.0.1,IP:0.0.0.0"

echo "🔐 Generating MinIO SSL Certificates..."
echo "Certificate Directory: $CERT_DIR"
echo "Validity: $CERT_VALIDITY_DAYS days"
echo "Common Name: $COMMON_NAME"
echo "SANs: $SANS"

# Create certificate directory
mkdir -p "$CERT_DIR"

# Generate private key
echo "📝 Generating private key..."
openssl genrsa -out "$CERT_DIR/private.key" 2048

# Create certificate signing request configuration
echo "📝 Creating certificate configuration..."
cat > "$CERT_DIR/cert.conf" << EOF
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C=$COUNTRY
ST=$STATE
L=$CITY
O=$ORGANIZATION
OU=$ORGANIZATIONAL_UNIT
CN=$COMMON_NAME
emailAddress=$EMAIL

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = minio
DNS.2 = localhost
DNS.3 = jadwa-minio
DNS.4 = 127.0.0.1
IP.1 = 127.0.0.1
IP.2 = 0.0.0.0
EOF

# Generate certificate signing request
echo "📝 Generating certificate signing request..."
openssl req -new -key "$CERT_DIR/private.key" -out "$CERT_DIR/cert.csr" -config "$CERT_DIR/cert.conf"

# Generate self-signed certificate
echo "📝 Generating self-signed certificate..."
openssl x509 -req -in "$CERT_DIR/cert.csr" -signkey "$CERT_DIR/private.key" -out "$CERT_DIR/public.crt" -days $CERT_VALIDITY_DAYS -extensions v3_req -extfile "$CERT_DIR/cert.conf"

# Create combined certificate file (some applications need this)
echo "📝 Creating combined certificate file..."
cat "$CERT_DIR/public.crt" "$CERT_DIR/private.key" > "$CERT_DIR/combined.pem"

# Set appropriate permissions
chmod 600 "$CERT_DIR/private.key"
chmod 644 "$CERT_DIR/public.crt"
chmod 644 "$CERT_DIR/combined.pem"

# Display certificate information
echo "✅ Certificate generation completed!"
echo ""
echo "📋 Certificate Details:"
openssl x509 -in "$CERT_DIR/public.crt" -text -noout | grep -A 1 "Subject:"
openssl x509 -in "$CERT_DIR/public.crt" -text -noout | grep -A 10 "Subject Alternative Name:"
echo ""
echo "📁 Generated Files:"
echo "  - Private Key: $CERT_DIR/private.key"
echo "  - Public Certificate: $CERT_DIR/public.crt"
echo "  - Combined PEM: $CERT_DIR/combined.pem"
echo "  - CSR: $CERT_DIR/cert.csr"
echo "  - Config: $CERT_DIR/cert.conf"
echo ""
echo "🔍 Certificate Validity:"
openssl x509 -in "$CERT_DIR/public.crt" -noout -dates
echo ""
echo "⚠️  Note: This is a self-signed certificate. Browsers will show security warnings."
echo "   For production use, consider using certificates from a trusted CA."
echo ""
echo "🐳 Next steps:"
echo "   1. Update docker-compose.yaml to use SSL configuration"
echo "   2. Update application configuration to use UseSSL=true"
echo "   3. Restart MinIO container"
