# MinIO Production Deployment Guide

## 🌐 Production Environment Configuration

This guide covers deploying MinIO with SSL on the production server at *****************.

## 📋 Environment Overview

### Production Environment
- **Server IP**: *************
- **MinIO API Port**: 9000 (HTTPS)
- **MinIO Console Port**: 9001 (HTTPS)
- **SSL**: Enabled with self-signed certificates

### Staging Environment
- **Server IP**: *************
- **MinIO API Port**: 9003 (HTTPS)
- **MinIO Console Port**: 9004 (HTTPS)
- **SSL**: Enabled with self-signed certificates

## 🚀 Deployment Steps

### 1. Generate Production Certificates

On your local machine or the production server:

```bash
# Use the production-specific certificate generator
./scripts/generate-minio-certs-production.sh
```

This generates certificates that include:
- `DNS:*************`
- `IP:*************`
- Standard localhost entries for development compatibility

### 2. Deploy to Production Server

#### Option A: Deploy from Local Machine

```bash
# Copy certificates to production server
scp -r ./certs/minio user@*************:/path/to/jadwa/certs/

# Copy Docker Compose files
scp docker-compose.yaml docker-compose.production.yaml user@*************:/path/to/jadwa/

# SSH to production server and start services
ssh user@*************
cd /path/to/jadwa
docker-compose -f docker-compose.yaml -f docker-compose.production.yaml up -d
```

#### Option B: Generate Certificates on Production Server

```bash
# SSH to production server
ssh user@*************
cd /path/to/jadwa

# Generate certificates on server
./scripts/generate-minio-certs-production.sh

# Start services
docker-compose -f docker-compose.yaml -f docker-compose.production.yaml up -d
```

### 3. Configure Firewall Rules

Ensure the following ports are open on the production server:

```bash
# For Production
sudo ufw allow 9000/tcp  # MinIO API
sudo ufw allow 9001/tcp  # MinIO Console

# For Staging (if on same server)
sudo ufw allow 9003/tcp  # MinIO API (Staging)
sudo ufw allow 9004/tcp  # MinIO Console (Staging)

# Reload firewall
sudo ufw reload
```

### 4. Verify Deployment

#### Test from Production Server (Local)

```bash
# Test API endpoint
curl -k https://localhost:9000/minio/health/live

# Test Console (should return HTML)
curl -k https://localhost:9001
```

#### Test from External Client

```bash
# Test API endpoint
curl -k https://*************:9000/minio/health/live

# Test Console access
curl -k https://*************:9001
```

#### Use the Production Test Script

```bash
# Test production environment
./scripts/test-minio-ssl-production.sh production

# Test staging environment
./scripts/test-minio-ssl-production.sh staging
```

## 🔧 Production Configuration Details

### Docker Compose Production Override

The `docker-compose.production.yaml` includes:

```yaml
services:
  minio:
    environment:
      # Production SSL Configuration
      MINIO_SERVER_URL: https://*************:9000
      MINIO_BROWSER_REDIRECT_URL: https://*************:9001

  jadwa-api:
    environment:
      - MinIOConfiguration__Endpoint=minio:9000
      - MinIOConfiguration__UseSSL=true
```

### Application Configuration

Update your production `appsettings.Production.json`:

```json
{
  "MinIOConfiguration": {
    "Endpoint": "minio:9000",
    "UseSSL": true,
    "AccessKey": "minioadmin",
    "SecretKey": "minioadmin",
    "DefaultBucket": "other",
    "Enabled": true
  }
}
```

## 🔍 Monitoring and Maintenance

### Health Checks

Set up monitoring for the following endpoints:

```bash
# Production
https://*************:9000/minio/health/live
https://*************:9001

# Staging
https://*************:9003/minio/health/live
https://*************:9004
```

### Log Monitoring

Monitor MinIO logs for SSL-related issues:

```bash
# View MinIO logs
docker logs jadwa-minio

# Follow logs in real-time
docker logs -f jadwa-minio

# Filter for SSL/TLS messages
docker logs jadwa-minio 2>&1 | grep -i ssl
```

### Certificate Renewal

Certificates expire after 365 days. Set up a renewal process:

```bash
# Create a renewal script
cat > /path/to/jadwa/scripts/renew-certs.sh << 'EOF'
#!/bin/bash
cd /path/to/jadwa
./scripts/generate-minio-certs-production.sh
docker-compose restart minio
EOF

# Make executable
chmod +x /path/to/jadwa/scripts/renew-certs.sh

# Add to crontab (renew 30 days before expiry)
# 0 2 1 * * /path/to/jadwa/scripts/renew-certs.sh
```

## 🛡️ Security Considerations

### Production Security Checklist

- [ ] **Change Default Credentials**: Replace `minioadmin/minioadmin`
- [ ] **Firewall Configuration**: Only allow necessary ports
- [ ] **SSL Certificate**: Consider CA-signed certificates for production
- [ ] **Access Logging**: Enable MinIO access logs
- [ ] **Backup Strategy**: Implement regular data backups
- [ ] **Network Security**: Use VPN or private networks when possible

### Recommended Production Changes

1. **Strong Credentials**:
   ```bash
   # Generate strong credentials
   MINIO_ROOT_USER=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-20)
   MINIO_ROOT_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-32)
   ```

2. **CA-Signed Certificates**:
   - Use Let's Encrypt for free SSL certificates
   - Or purchase certificates from a trusted CA

3. **Access Policies**:
   - Implement proper bucket policies
   - Use IAM users instead of root credentials

## 🚨 Troubleshooting

### Common Production Issues

1. **Certificate Validation Errors**:
   ```bash
   # Check if certificate includes production IP
   openssl x509 -in ./certs/minio/public.crt -text -noout | grep "*************"
   ```

2. **Firewall Blocking Connections**:
   ```bash
   # Test port connectivity
   telnet ************* 9000
   telnet ************* 9001
   ```

3. **Docker Container Issues**:
   ```bash
   # Check container status
   docker ps | grep jadwa-minio
   
   # Check container logs
   docker logs jadwa-minio
   
   # Restart container
   docker-compose restart minio
   ```

4. **SSL Handshake Failures**:
   ```bash
   # Test SSL connection
   openssl s_client -connect *************:9000 -servername *************
   ```

### Emergency Recovery

If MinIO fails to start with SSL:

1. **Disable SSL temporarily**:
   ```bash
   # Edit docker-compose.production.yaml
   # Change UseSSL to false
   # Remove SSL environment variables
   docker-compose restart minio
   ```

2. **Regenerate certificates**:
   ```bash
   rm -rf ./certs/minio/*
   ./scripts/generate-minio-certs-production.sh
   docker-compose restart minio
   ```

## 📞 Support Contacts

For production issues:
- **System Administrator**: [Contact Info]
- **Development Team**: [Contact Info]
- **Emergency Escalation**: [Contact Info]

## 📚 Additional Resources

- [MinIO SSL Setup Guide](./MinIO-SSL-Setup.md)
- [MinIO Official Documentation](https://docs.min.io/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [OpenSSL Certificate Guide](https://www.openssl.org/docs/)
