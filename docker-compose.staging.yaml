services:
  minio:
    ports:
      - "9003:9000"   # MinIO API port (HTTPS) - Staging uses port 9003
      - "9004:9001"   # MinIO Console (Web UI) port (HTTPS) - Staging uses port 9004
    environment:
      # Staging SSL Configuration
      MINIO_SERVER_URL: https://65.109.156.72:9003
      MINIO_BROWSER_REDIRECT_URL: https://65.109.156.72:9004

  jadwa-api:
    ports:
      - '44301:8080'
      - '7010:8081'
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - MinIOConfiguration__Endpoint=minio:9000
      - MinIOConfiguration__AccessKey=minioadmin
      - MinIOConfiguration__SecretKey=minioadmin
      - MinIOConfiguration__UseSSL=true
      - MinIOConfiguration__Enabled=true