using Application.Features.Identity.Users.Dtos;
using Application.Features.Resolutions.Dtos;
using Domain.Entities.Shared;
using Domain.Entities.Users;

namespace Application.Mapping.Users
{
    public partial class UserProfile
    {
        public void GetUserProfileMapping()
        {
            CreateMap<User, UserProfileResponseDto>()
                .ForMember(dest => dest.LastUpdateDate, opt => opt.MapFrom(c=> c.UpdatedAt.HasValue ? c.UpdatedAt : null ))
                .ForMember(dest => dest.Roles, opt => opt.MapFrom(c => c.Roles.Select(c=>c.Name)));
        }
    }
}
