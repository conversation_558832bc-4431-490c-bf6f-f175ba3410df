#!/bin/bash

# MinIO SSL Setup Script
# This script generates certificates and starts MinIO with SSL

set -e

ENVIRONMENT=${1:-development}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo "🚀 Setting up MinIO with SSL for $ENVIRONMENT environment..."

# Change to project directory
cd "$PROJECT_DIR"

# Generate certificates if they don't exist
if [ ! -f "./certs/minio/private.key" ] || [ ! -f "./certs/minio/public.crt" ]; then
    echo "📜 Generating SSL certificates..."
    chmod +x scripts/generate-minio-certs.sh
    ./scripts/generate-minio-certs.sh
else
    echo "✅ SSL certificates already exist"
    
    # Check if certificates are about to expire (within 30 days)
    if command -v openssl >/dev/null 2>&1; then
        EXPIRY_DATE=$(openssl x509 -in "./certs/minio/public.crt" -noout -enddate | cut -d= -f2)
        EXPIRY_TIMESTAMP=$(date -d "$EXPIRY_DATE" +%s 2>/dev/null || date -j -f "%b %d %H:%M:%S %Y %Z" "$EXPIRY_DATE" +%s 2>/dev/null || echo "0")
        CURRENT_TIMESTAMP=$(date +%s)
        DAYS_UNTIL_EXPIRY=$(( (EXPIRY_TIMESTAMP - CURRENT_TIMESTAMP) / 86400 ))
        
        if [ "$DAYS_UNTIL_EXPIRY" -lt 30 ] && [ "$DAYS_UNTIL_EXPIRY" -gt 0 ]; then
            echo "⚠️  Certificate expires in $DAYS_UNTIL_EXPIRY days. Consider renewing soon."
        elif [ "$DAYS_UNTIL_EXPIRY" -le 0 ]; then
            echo "❌ Certificate has expired! Generating new certificates..."
            ./scripts/generate-minio-certs.sh
        fi
    fi
fi

# Stop existing containers
echo "🛑 Stopping existing containers..."
docker-compose down || true

# Start services based on environment
echo "🐳 Starting MinIO with SSL in $ENVIRONMENT mode..."

case $ENVIRONMENT in
    "development"|"dev")
        docker-compose -f docker-compose.yaml -f docker-compose.development.yaml up -d
        ;;
    "production"|"prod")
        docker-compose -f docker-compose.yaml -f docker-compose.production.yaml up -d
        ;;
    "staging"|"stage")
        docker-compose -f docker-compose.yaml -f docker-compose.staging.yaml up -d
        ;;
    *)
        echo "❌ Unknown environment: $ENVIRONMENT"
        echo "Usage: $0 [development|production|staging]"
        exit 1
        ;;
esac

# Wait for services to start
echo "⏳ Waiting for services to start..."
sleep 10

# Check if MinIO is running
if docker ps | grep -q "jadwa-minio"; then
    echo "✅ MinIO is running with SSL!"
    echo ""
    echo "🌐 Access URLs:"
    echo "   MinIO API: https://localhost:9000"
    echo "   MinIO Console: https://localhost:9001"
    echo "   Username: minioadmin"
    echo "   Password: minioadmin"
    echo ""
    echo "⚠️  Note: Browsers will show security warnings for self-signed certificates."
    echo "   Click 'Advanced' and 'Proceed to localhost' to continue."
    echo ""
    echo "📋 To check logs:"
    echo "   docker logs jadwa-minio"
    echo "   docker logs jadwa-minio-setup"
else
    echo "❌ Failed to start MinIO. Check logs:"
    echo "   docker logs jadwa-minio"
    exit 1
fi

# Show container status
echo "📊 Container Status:"
docker ps --filter "name=jadwa-" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
