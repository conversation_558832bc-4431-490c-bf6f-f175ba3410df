#!/bin/bash

# MinIO SSL Test Script
# This script tests the SSL configuration of MinIO

set -e

echo "🧪 Testing MinIO SSL Configuration..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test functions
test_certificate_files() {
    echo -e "${BLUE}📁 Checking certificate files...${NC}"
    
    if [ -f "./certs/minio/private.key" ]; then
        echo -e "${GREEN}✅ Private key exists${NC}"
    else
        echo -e "${RED}❌ Private key missing${NC}"
        return 1
    fi
    
    if [ -f "./certs/minio/public.crt" ]; then
        echo -e "${GREEN}✅ Public certificate exists${NC}"
    else
        echo -e "${RED}❌ Public certificate missing${NC}"
        return 1
    fi
    
    # Check certificate validity
    if command -v openssl >/dev/null 2>&1; then
        echo -e "${BLUE}🔍 Certificate details:${NC}"
        openssl x509 -in "./certs/minio/public.crt" -noout -subject -dates -ext subjectAltName
    fi
}

test_container_status() {
    echo -e "${BLUE}🐳 Checking container status...${NC}"
    
    if docker ps | grep -q "jadwa-minio"; then
        echo -e "${GREEN}✅ MinIO container is running${NC}"
        
        # Show container details
        docker ps --filter "name=jadwa-minio" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    else
        echo -e "${RED}❌ MinIO container is not running${NC}"
        return 1
    fi
}

test_ssl_connectivity() {
    echo -e "${BLUE}🔗 Testing SSL connectivity...${NC}"
    
    # Test API endpoint
    if curl -k -s -f "https://localhost:9000/minio/health/live" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ MinIO API (port 9000) is accessible via HTTPS${NC}"
    else
        echo -e "${RED}❌ MinIO API (port 9000) is not accessible via HTTPS${NC}"
        return 1
    fi
    
    # Test Console endpoint
    if curl -k -s -f "https://localhost:9001" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ MinIO Console (port 9001) is accessible via HTTPS${NC}"
    else
        echo -e "${YELLOW}⚠️  MinIO Console (port 9001) test inconclusive (may redirect)${NC}"
    fi
}

test_certificate_details() {
    echo -e "${BLUE}📋 Certificate validation details...${NC}"
    
    if command -v openssl >/dev/null 2>&1; then
        # Test certificate against localhost
        echo -e "${BLUE}Testing certificate for localhost:9000...${NC}"
        if echo | openssl s_client -connect localhost:9000 -servername localhost 2>/dev/null | openssl x509 -noout -text | grep -q "DNS:localhost"; then
            echo -e "${GREEN}✅ Certificate includes localhost in SANs${NC}"
        else
            echo -e "${YELLOW}⚠️  Certificate may not include localhost in SANs${NC}"
        fi
        
        # Show certificate expiry
        EXPIRY=$(openssl x509 -in "./certs/minio/public.crt" -noout -enddate | cut -d= -f2)
        echo -e "${BLUE}Certificate expires: ${EXPIRY}${NC}"
        
        # Calculate days until expiry
        if command -v date >/dev/null 2>&1; then
            EXPIRY_TIMESTAMP=$(date -d "$EXPIRY" +%s 2>/dev/null || date -j -f "%b %d %H:%M:%S %Y %Z" "$EXPIRY" +%s 2>/dev/null || echo "0")
            CURRENT_TIMESTAMP=$(date +%s)
            DAYS_UNTIL_EXPIRY=$(( (EXPIRY_TIMESTAMP - CURRENT_TIMESTAMP) / 86400 ))
            
            if [ "$DAYS_UNTIL_EXPIRY" -gt 30 ]; then
                echo -e "${GREEN}✅ Certificate valid for $DAYS_UNTIL_EXPIRY more days${NC}"
            elif [ "$DAYS_UNTIL_EXPIRY" -gt 0 ]; then
                echo -e "${YELLOW}⚠️  Certificate expires in $DAYS_UNTIL_EXPIRY days${NC}"
            else
                echo -e "${RED}❌ Certificate has expired!${NC}"
            fi
        fi
    else
        echo -e "${YELLOW}⚠️  OpenSSL not available for detailed certificate testing${NC}"
    fi
}

test_docker_logs() {
    echo -e "${BLUE}📝 Checking MinIO logs for SSL-related messages...${NC}"
    
    if docker ps | grep -q "jadwa-minio"; then
        echo -e "${BLUE}Recent MinIO logs:${NC}"
        docker logs --tail 20 jadwa-minio 2>&1 | grep -E "(SSL|TLS|certificate|https)" || echo "No SSL-related log messages found"
    else
        echo -e "${RED}❌ MinIO container not running${NC}"
    fi
}

# Main test execution
main() {
    echo -e "${BLUE}🚀 Starting MinIO SSL tests...${NC}"
    echo ""
    
    local failed_tests=0
    
    # Run tests
    test_certificate_files || ((failed_tests++))
    echo ""
    
    test_container_status || ((failed_tests++))
    echo ""
    
    test_ssl_connectivity || ((failed_tests++))
    echo ""
    
    test_certificate_details
    echo ""
    
    test_docker_logs
    echo ""
    
    # Summary
    if [ $failed_tests -eq 0 ]; then
        echo -e "${GREEN}🎉 All SSL tests passed!${NC}"
        echo -e "${GREEN}MinIO is properly configured with SSL${NC}"
        echo ""
        echo -e "${BLUE}Access URLs:${NC}"
        echo "  MinIO API: https://localhost:9000"
        echo "  MinIO Console: https://localhost:9001"
        echo "  Username: minioadmin"
        echo "  Password: minioadmin"
    else
        echo -e "${RED}❌ $failed_tests test(s) failed${NC}"
        echo -e "${YELLOW}Please check the configuration and try again${NC}"
        return 1
    fi
}

# Check if we're in the right directory
if [ ! -f "docker-compose.yaml" ]; then
    echo -e "${RED}❌ Please run this script from the project root directory${NC}"
    exit 1
fi

# Run main function
main
