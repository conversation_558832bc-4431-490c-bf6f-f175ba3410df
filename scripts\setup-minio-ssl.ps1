# MinIO SSL Setup Script for Windows PowerShell
# This script generates certificates and starts MinIO with SSL

param(
    [string]$Environment = "development"
)

$ErrorActionPreference = "Stop"

$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectDir = Split-Path -Parent $ScriptDir

Write-Host "🚀 Setting up MinIO with SSL for $Environment environment..." -ForegroundColor Green

# Change to project directory
Set-Location $ProjectDir

# Generate certificates if they don't exist
$PrivateKeyPath = "./certs/minio/private.key"
$PublicCertPath = "./certs/minio/public.crt"

if (!(Test-Path $PrivateKeyPath) -or !(Test-Path $PublicCertPath)) {
    Write-Host "📜 Generating SSL certificates..." -ForegroundColor Cyan
    & .\scripts\generate-minio-certs.ps1
} else {
    Write-Host "✅ SSL certificates already exist" -ForegroundColor Green
    
    # Check if certificates are about to expire (within 30 days)
    try {
        $ExpiryOutput = & openssl x509 -in $PublicCertPath -noout -enddate 2>$null
        if ($ExpiryOutput -match "notAfter=(.+)") {
            $ExpiryDate = [DateTime]::ParseExact($matches[1], "MMM dd HH:mm:ss yyyy GMT", $null)
            $DaysUntilExpiry = ($ExpiryDate - (Get-Date)).Days
            
            if ($DaysUntilExpiry -lt 30 -and $DaysUntilExpiry -gt 0) {
                Write-Host "⚠️  Certificate expires in $DaysUntilExpiry days. Consider renewing soon." -ForegroundColor Yellow
            } elseif ($DaysUntilExpiry -le 0) {
                Write-Host "❌ Certificate has expired! Generating new certificates..." -ForegroundColor Red
                & .\scripts\generate-minio-certs.ps1
            }
        }
    } catch {
        Write-Host "⚠️  Could not check certificate expiry" -ForegroundColor Yellow
    }
}

# Stop existing containers
Write-Host "🛑 Stopping existing containers..." -ForegroundColor Yellow
try {
    & docker-compose down 2>$null
} catch {
    # Ignore errors if no containers are running
}

# Start services based on environment
Write-Host "🐳 Starting MinIO with SSL in $Environment mode..." -ForegroundColor Cyan

switch ($Environment.ToLower()) {
    { $_ -in @("development", "dev") } {
        & docker-compose -f docker-compose.yaml -f docker-compose.development.yaml up -d
    }
    { $_ -in @("production", "prod") } {
        & docker-compose -f docker-compose.yaml -f docker-compose.production.yaml up -d
    }
    { $_ -in @("staging", "stage") } {
        & docker-compose -f docker-compose.yaml -f docker-compose.staging.yaml up -d
    }
    default {
        Write-Host "❌ Unknown environment: $Environment" -ForegroundColor Red
        Write-Host "Usage: .\setup-minio-ssl.ps1 [development|production|staging]" -ForegroundColor Yellow
        exit 1
    }
}

# Wait for services to start
Write-Host "⏳ Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check if MinIO is running
$MinIORunning = & docker ps --filter "name=jadwa-minio" --format "{{.Names}}" | Select-String "jadwa-minio"

if ($MinIORunning) {
    Write-Host "✅ MinIO is running with SSL!" -ForegroundColor Green
    Write-Host ""
    Write-Host "🌐 Access URLs:" -ForegroundColor Yellow
    Write-Host "   MinIO API: https://localhost:9000"
    Write-Host "   MinIO Console: https://localhost:9001"
    Write-Host "   Username: minioadmin"
    Write-Host "   Password: minioadmin"
    Write-Host ""
    Write-Host "⚠️  Note: Browsers will show security warnings for self-signed certificates." -ForegroundColor Red
    Write-Host "   Click 'Advanced' and 'Proceed to localhost' to continue." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "📋 To check logs:" -ForegroundColor Yellow
    Write-Host "   docker logs jadwa-minio"
    Write-Host "   docker logs jadwa-minio-setup"
} else {
    Write-Host "❌ Failed to start MinIO. Check logs:" -ForegroundColor Red
    Write-Host "   docker logs jadwa-minio"
    exit 1
}

# Show container status
Write-Host ""
Write-Host "📊 Container Status:" -ForegroundColor Yellow
& docker ps --filter "name=jadwa-" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
