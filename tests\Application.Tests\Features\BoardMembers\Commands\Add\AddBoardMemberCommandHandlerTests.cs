using Abstraction.Base.Response;
using Abstraction.Constants;
using Abstraction.Contract.Service;
using Abstraction.Contracts.Identity;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Application.Features.BoardMembers.Commands.Add;
using AutoMapper;
using Domain.Entities.FundManagement;
using Domain.Entities.Notifications;
using Domain.Entities.Users;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Localization;
using Moq;
using Resources;
using Xunit;
using System.Security.Claims;
using Core.Abstraction.Contract.Service.Notifications;
using Abstraction.Enums;

namespace Application.Tests.Features.BoardMembers.Commands.Add
{
    public class AddBoardMemberCommandHandlerTests
    {
        private readonly Mock<IRepositoryManager> _mockRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IStringLocalizer<SharedResources>> _mockLocalizer;
        private readonly Mock<ILoggerManager> _mockLogger;
        private readonly Mock<IIdentityServiceManager> _mockIdentityService;
        private readonly Mock<ICurrentUserService> _mockCurrentUserService;
        private readonly Mock<IWhatsAppNotificationService> _mockWhatsAppService;
        private readonly AddBoardMemberCommandHandler _handler;

        public AddBoardMemberCommandHandlerTests()
        {
            _mockRepository = new Mock<IRepositoryManager>();
            _mockMapper = new Mock<IMapper>();
            _mockLocalizer = new Mock<IStringLocalizer<SharedResources>>();
            _mockLogger = new Mock<ILoggerManager>();
            _mockIdentityService = new Mock<IIdentityServiceManager>();
            _mockCurrentUserService = new Mock<ICurrentUserService>();
            _mockWhatsAppService = new Mock<IWhatsAppNotificationService>();

            _handler = new AddBoardMemberCommandHandler(
                _mockRepository.Object,
                _mockMapper.Object,
                _mockLocalizer.Object,
                _mockLogger.Object,
                _mockIdentityService.Object,
                _mockCurrentUserService.Object,
                _mockWhatsAppService.Object
            );

            // Setup common localizer mocks
            _mockLocalizer.Setup(x => x[SharedResourcesKey.AnErrorIsOccurredWhileSavingData])
                .Returns(new LocalizedString(SharedResourcesKey.AnErrorIsOccurredWhileSavingData, "An error occurred while saving data"));
            _mockLocalizer.Setup(x => x[SharedResourcesKey.FundNotFound])
                .Returns(new LocalizedString(SharedResourcesKey.FundNotFound, "Fund not found"));
            _mockLocalizer.Setup(x => x[SharedResourcesKey.ErrorReachedMaxIndependentBoard])
                .Returns(new LocalizedString(SharedResourcesKey.ErrorReachedMaxIndependentBoard, "Reached maximum independent board members"));
            _mockLocalizer.Setup(x => x[SharedResourcesKey.BoardMemberAddedSuccessfully])
                .Returns(new LocalizedString(SharedResourcesKey.BoardMemberAddedSuccessfully, "Board member added successfully"));
            _mockLocalizer.Setup(x => x[SharedResourcesKey.SystemErrorSavingData])
                .Returns(new LocalizedString(SharedResourcesKey.SystemErrorSavingData, "System error saving data"));
        }

        [Fact]
        public async Task Handle_RequestIsNull_ReturnsBadRequest()
        {
            // Arrange
            AddBoardMemberCommand request = null;

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.Succeeded);
            Assert.Equal("An error occurred while saving data", result.Message);
        }

        [Fact]
        public async Task Handle_FundNotFound_ReturnsNotFound()
        {
            // Arrange
            var request = new AddBoardMemberCommand { FundId = 1, UserId = 1, MemberType = BoardMemberType.Independent, IsChairman = false };
            _mockRepository.Setup(r => r.Funds.ViewFundUsers(request.FundId, false)).ReturnsAsync((Fund)null);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.Succeeded);
            Assert.Equal("Fund not found", result.Message);
        }

        [Fact]
        public async Task Handle_ReachedMaxIndependentMembers_ReturnsServerError()
        {
            // Arrange
            var request = new AddBoardMemberCommand { FundId = 1, UserId = 1, MemberType = BoardMemberType.Independent, IsChairman = false };
            var fund = new Fund { Id = 1, Name = "Test Fund", BoardMembers = new List<BoardMember>() };
            for (int i = 0; i < 14; i++)
            {
                fund.BoardMembers.Add(new BoardMember { MemberType = BoardMemberType.Independent });
            }
            _mockRepository.Setup(r => r.Funds.ViewFundUsers(request.FundId, false)).ReturnsAsync(fund);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.Succeeded);
            Assert.Equal("Reached maximum independent board members", result.Message);
        }

        [Fact]
        public async Task Handle_ValidRequest_AddsBoardMemberAndReturnsSuccess()
        {
            // Arrange
            var request = new AddBoardMemberCommand { FundId = 1, UserId = 1, MemberType = BoardMemberType.Independent, IsChairman = false };
            var fund = new Fund { Id = 1, Name = "Test Fund", BoardMembers = new List<BoardMember>() };
            var boardMember = new BoardMember { Id = 1, FundId = 1, UserId = 1, MemberType = BoardMemberType.Independent, IsChairman = false, IsActive = true };

            _mockRepository.Setup(r => r.Funds.ViewFundUsers(request.FundId, false)).ReturnsAsync(fund);
            _mockMapper.Setup(m => m.Map<BoardMember>(request)).Returns(boardMember);
            _mockRepository.Setup(r => r.BoardMembers.AddAsync(It.IsAny<BoardMember>(), It.IsAny<CancellationToken>())).ReturnsAsync(boardMember);

            // Mock for notifications and other dependencies as needed
            _mockCurrentUserService.Setup(c => c.UserId).Returns(2);
            _mockCurrentUserService.Setup(c => c.UserName).Returns("TestUser");
            _mockIdentityService.Setup(i => i.UserManagmentService.FindByIdAsync("1")).ReturnsAsync(new User { Id = 1, FullName = "Board Member" });

            // Mock for WhatsApp
            _mockIdentityService.Setup(i => i.UserManagmentService.FindByIdWithRolesAsync("1")).ReturnsAsync(new User { Id = 1, CountryCode = "1", PhoneNumber = "1234567890", Roles = new List<string> { "Role1" } });
            _mockWhatsAppService.Setup(w => w.SendLocalizedMessageAsync(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<WhatsAppMessageType>(), It.IsAny<object[]>(), It.IsAny<CancellationToken>())).ReturnsAsync(new WhatsAppResponse { IsSuccess = true });

            // Mock for fund activation check
            _mockRepository.Setup(r => r.BoardMembers.IndependentMembersCountAsync(1)).ReturnsAsync(2);
            _mockRepository.Setup(r => r.Funds.UpdateAsync(It.IsAny<Fund>())).Returns(Task.CompletedTask);
            _mockRepository.Setup(r => r.Notifications.AddRangeAsync(It.IsAny<List<Notification>>())).Returns(Task.CompletedTask);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.True(result.Succeeded);
            Assert.Equal("Board member added successfully", result.Message);
            _mockRepository.Verify(r => r.BoardMembers.AddAsync(It.IsAny<BoardMember>(), It.IsAny<CancellationToken>()), Times.Once);
            _mockWhatsAppService.Verify(w => w.SendLocalizedMessageAsync(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<WhatsAppMessageType>(), It.IsAny<object[]>(), It.IsAny<CancellationToken>()), Times.Once);
            _mockRepository.Verify(r => r.Funds.UpdateAsync(It.IsAny<Fund>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ExceptionOccurs_ReturnsServerError()
        {
            // Arrange
            var request = new AddBoardMemberCommand { FundId = 1, UserId = 1, MemberType = BoardMemberType.Independent, IsChairman = false };
            _mockRepository.Setup(r => r.Funds.ViewFundUsers(request.FundId, false)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.Succeeded);
            Assert.Equal("System error saving data", result.Message);
            _mockLogger.Verify(l => l.LogError(It.IsAny<Exception>(), It.IsAny<string>()), Times.Once);
        }

        // Add more tests as needed for other scenarios, like fund activation, notifications, etc.
    }
}
