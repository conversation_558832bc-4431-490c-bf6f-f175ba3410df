using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Abstraction.Contracts.Identity;
using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Domain.Entities.Users;
using Microsoft.Extensions.Localization;
using Resources;
using Abstraction.Contract.Service;

using Application.Features.Identity.Users.Dtos;
using Abstraction.Constants;
using Abstraction.Contracts.Service;
using Abstraction.Enums;
using Application.Common.Configurations;
using Microsoft.Extensions.Options;
using Abstraction.Contract.Service.Storage;
using Abstraction.Contracts.Repository;
using Domain.Entities.Shared;
using Application.Common.Helpers;
using Application.Features.Identity.Users.Queries.Responses;

namespace Application.Features.Identity.Users.Queries.GetUserProfile
{
    /// <summary>
    /// Handler for getting user profile information
    /// Implements Clean Architecture and CQRS patterns
    /// </summary>
    public class GetUserProfileQueryHandler : BaseResponseHandler, IQueryHandler<GetUserProfileQuery, BaseResponse<UserProfileResponseDto>>
    {
        #region Fields
        private readonly IIdentityServiceManager _identityServiceManager;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ICurrentUserService _currentUserService;
        private readonly IServiceManager _serviceManager;
        private readonly MinIOConfiguration _minioConfig;
        private readonly IRepositoryManager _repository;

        #endregion

        #region Constructor
        public GetUserProfileQueryHandler(
            IMapper mapper,
            IIdentityServiceManager identityServiceManager ,
            IStringLocalizer<SharedResources> localizer,
            ICurrentUserService currentUserService,
            IServiceManager serviceManager,
            IOptions<MinIOConfiguration> minioConfig,
            IRepositoryManager repository)
        {
            _identityServiceManager = identityServiceManager;
            _mapper = mapper;
            _localizer = localizer;
            _currentUserService = currentUserService;
            _serviceManager = serviceManager;
            _minioConfig = minioConfig.Value;
            _repository = repository;
        }
        #endregion

        #region Handler
        public async Task<BaseResponse<UserProfileResponseDto>> Handle(GetUserProfileQuery request, CancellationToken cancellationToken)
        {
            try
            {
                // Determine which user profile to get
                var targetUserId = request.UserId ?? _currentUserService.UserId.GetValueOrDefault();
                var currentUserId = _currentUserService.UserId.GetValueOrDefault();

                // Authorization check - users can only access their own profile unless they're admin
                if (targetUserId != currentUserId)
                {
                    var currentUser = await _identityServiceManager.UserManagmentService.FindByIdAsync(currentUserId.ToString());
                    if (currentUser == null)
                    {
                        return Unauthorized<UserProfileResponseDto>(_localizer[SharedResourcesKey.UserNotFound]);
                    }

                    var currentUserRoles = await _identityServiceManager.UserManagmentService.GetUserRolesAsync(currentUser);
                    if (!currentUserRoles.Contains("admin") && !currentUserRoles.Contains("superadmin"))
                    {
                        return Unauthorized<UserProfileResponseDto>(_localizer[SharedResourcesKey.UnauthorizedUserAccess]);
                    }
                }

                // Get user by ID
                var user = await _identityServiceManager.UserManagmentService.FindByIdWithAttachmentAndRolesAsync(targetUserId.ToString());
                if (user == null)
                {
                    return NotFound<UserProfileResponseDto>(_localizer[SharedResourcesKey.UserNotFound]);
                }

                // Map to response DTO
                var response = _mapper.Map<UserProfileResponseDto>(user);
                var LegalCouncilHasActiveUser = await HasActiveUserInRoleAsync(RoleHelper.LegalCouncil,user.Id);
                var FinanceControllerHasActiveUser = await HasActiveUserInRoleAsync(RoleHelper.FinanceController, user.Id);
                var ComplianceLegalManagingDirectorHasActiveUser = await HasActiveUserInRoleAsync(RoleHelper.ComplianceLegalManagingDirector, user.Id);
                var HeadOfRealEstateHasActiveUser = await HasActiveUserInRoleAsync(RoleHelper.HeadOfRealEstate, user.Id);
                // Check each single-holder role for active user assignment
                response.LegalCouncilHasActiveUser = LegalCouncilHasActiveUser.HasActiveUser;
                response.FinanceControllerHasActiveUser = FinanceControllerHasActiveUser.HasActiveUser;
                response.ComplianceLegalManagingDirectorHasActiveUser = ComplianceLegalManagingDirectorHasActiveUser.HasActiveUser;
                response.HeadOfRealEstateHasActiveUser = HeadOfRealEstateHasActiveUser.HasActiveUser;
                response.LegalCouncilUserFullName = LegalCouncilHasActiveUser.UserFullName ?? string.Empty;
                response.FinanceControllerUserFullName = FinanceControllerHasActiveUser.UserFullName ?? string.Empty;
                response.ComplianceLegalManagingDirectorUserFullName = ComplianceLegalManagingDirectorHasActiveUser.UserFullName ?? string.Empty;
                response.HeadOfRealEstateUserFullName = HeadOfRealEstateHasActiveUser.UserFullName ?? string.Empty;

                ApplyRoleLocalization(response);
                if (response.PersonalPhoto is null)
                    response.PersonalPhoto = new Resolutions.Dtos.AttachmentDto();
                //if (response.CvFile is null)
                    //response.CvFile = new Resolutions.Dtos.AttachmentDto();
                return Success(response);
            }
            catch (Exception ex)
            {
                return ServerError<UserProfileResponseDto>(_localizer[SharedResourcesKey.SystemErrorRetrievingData]);
            }
        }
        #endregion

        #region Private Methods
      
        /// <summary>
        /// Checks if a specific role has an active user assigned
        /// </summary>
        /// <param name="roleName">Name of the role to check</param>
        /// <returns>True if there is an active user assigned to the role</returns>
        private async Task<(bool HasActiveUser, string? UserFullName)> HasActiveUserInRoleAsync(string roleName, int userId)
        {
            try
            {
                // Get all users in the specified role
                var usersInRole = await _identityServiceManager.UserManagmentService.GetUsersByRole(roleName);

                // Check if any of the users are active
                var hasActiveUser = usersInRole.Any(user => user.Id != userId && user.IsActive);



                return (hasActiveUser, hasActiveUser ? usersInRole.Where(c => c.IsActive).FirstOrDefault().FullName : null);
            }
            catch (Exception ex)
            {
                
                return (false, null); // Default to false on error
            }
        }

        /// <summary>
        /// Apply localization to role names in user response DTOs
        /// Converts database role names to localized display names based on user's preferred language
        /// </summary>
        /// <param name="userResponses">List of user response DTOs to localize</param>
        private void ApplyRoleLocalization(UserProfileResponseDto userResponse)
        {
            // Localize all role names
                if (userResponse.Roles != null && userResponse.Roles.Count > 0)
                {
                    userResponse.Roles = [.. userResponse.Roles
                        .Select(roleName => LocalizationHelper.GetUserRoleDisplay(roleName, _localizer))];
                }
        }


        #endregion
    }
}
